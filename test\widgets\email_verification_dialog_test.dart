import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/widgets/email_verification_dialog.dart';
import 'package:upshift/theme/theme.dart';

void main() {
  group('EmailVerificationDialog', () {
    Widget createTestWidget({
      String? userEmail,
      VoidCallback? onVerificationComplete,
      bool showResendOption = true,
    }) {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () => showDialog(
                context: context,
                builder: (context) => EmailVerificationDialog(
                  userEmail: userEmail,
                  onVerificationComplete: onVerificationComplete,
                  showResendOption: showResendOption,
                ),
              ),
              child: const Text('Show Dialog'),
            ),
          ),
        ),
      );
    }

    testWidgets('displays dialog with required elements', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Email Verification Required'), findsOneWidget);
      expect(find.text('Please verify your email address to continue using Upshift.'), findsOneWidget);
      expect(find.byIcon(AppIcons.emailUnverified), findsOneWidget);
      expect(find.text('Later'), findsOneWidget);
      expect(find.text('Check'), findsOneWidget);
    });

    testWidgets('displays user email when provided', (WidgetTester tester) async {
      // Arrange
      const testEmail = '<EMAIL>';
      
      // Act
      await tester.pumpWidget(createTestWidget(userEmail: testEmail));
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text(testEmail), findsOneWidget);
    });

    testWidgets('shows resend button when showResendOption is true', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(showResendOption: true));
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Resend'), findsOneWidget);
    });

    testWidgets('hides resend button when showResendOption is false', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(showResendOption: false));
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Resend'), findsNothing);
    });

    testWidgets('later button dismisses dialog with false result', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Later'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(EmailVerificationDialog), findsNothing);
    });

    testWidgets('check button shows loading state when tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Check'));
      await tester.pump(); // Trigger rebuild

      // Assert
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    });

    testWidgets('resend button shows loading state when tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget(showResendOption: true));
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Resend'));
      await tester.pump(); // Trigger rebuild

      // Assert
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    });

    group('showEmailVerificationDialog utility function', () {
      testWidgets('shows dialog and returns result', (WidgetTester tester) async {
        // Arrange
        bool? result;
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () async {
                    result = await showEmailVerificationDialog(context);
                  },
                  child: const Text('Show Dialog'),
                ),
              ),
            ),
          ),
        );

        // Act
        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(EmailVerificationDialog), findsOneWidget);

        // Dismiss dialog
        await tester.tap(find.text('Later'));
        await tester.pumpAndSettle();

        expect(result, false);
      });

      testWidgets('passes parameters correctly', (WidgetTester tester) async {
        // Arrange
        const testEmail = '<EMAIL>';
        bool callbackCalled = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () async {
                    await showEmailVerificationDialog(
                      context,
                      userEmail: testEmail,
                      onVerificationComplete: () => callbackCalled = true,
                      showResendOption: false,
                    );
                  },
                  child: const Text('Show Dialog'),
                ),
              ),
            ),
          ),
        );

        // Act
        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text(testEmail), findsOneWidget);
        expect(find.text('Resend'), findsNothing); // showResendOption: false
      });
    });

    group('Accessibility', () {
      testWidgets('dialog has proper semantic structure', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('Email Verification Required'), findsOneWidget);
      });

      testWidgets('buttons are accessible', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Assert
        final laterButton = find.widgetWithText(TextButton, 'Later');
        final checkButton = find.widgetWithText(ElevatedButton, 'Check');
        
        expect(laterButton, findsOneWidget);
        expect(checkButton, findsOneWidget);
        
        // Verify buttons are enabled
        expect(tester.widget<TextButton>(laterButton).onPressed, isNotNull);
        expect(tester.widget<ElevatedButton>(checkButton).onPressed, isNotNull);
      });
    });
  });
}
