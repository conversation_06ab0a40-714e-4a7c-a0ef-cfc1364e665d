import '../../models/models.dart';
import '../../services/firestore.dart';

/// Utility class for seeding default guided paths and their steps into Firestore
class GuidedPathSeeder {
  /// Returns the list of 4 predefined GuidedPath entities with their steps
  static List<Map<String, dynamic>> getDefaultPathsWithSteps() {
    final now = DateTime.now();

    return [
      // Focus & Productivity → "Zero-to-Flow"
      {
        'path': GuidedPath(
          name: 'Zero-to-Flow',
          category: 'Focus & Productivity',
          description:
              'Transform scattered attention into laser-focused productivity. Master the art of deep work and sustained concentration through proven techniques.',
          stepCount: 6,
          targetUserTier: 'free', // Starter path for free users
          imageUrl: 'assets/guided-paths/zero-to-flow.jpg',
          estimatedCompletionTimeMinutes: 180, // 3 hours total
          difficultyLevel: 'beginner',
          prerequisites: [],
          isActive: true,
          createdAt: now,
          metadata: {
            'featured': true,
            'category_order': 1,
            'tags': ['focus', 'productivity', 'deep-work', 'concentration'],
          },
        ),
        'steps': [
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 1,
            title: 'Goal Setting Foundation',
            description:
                'Define clear, actionable goals that align with your values and priorities.',
            completionCriteria:
                'Write down 3 specific, measurable goals for the next 30 days',
            estimatedDurationMinutes: 25,
            resources: [
              'https://example.com/smart-goals-guide',
              'Goal Setting Worksheet (PDF)',
            ],
            reflectionPrompts: [
              'What are the most important outcomes I want to achieve?',
              'How will I know when I\'ve succeeded?',
              'What obstacles might I face and how can I prepare?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'foundation', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 2,
            title: 'Time-Blocking Mastery',
            description:
                'Learn to structure your day with intentional time blocks for maximum productivity.',
            completionCriteria:
                'Create and follow a time-blocked schedule for 3 consecutive days',
            estimatedDurationMinutes: 35,
            resources: [
              'https://example.com/time-blocking-tutorial',
              'Time-Blocking Template (Google Calendar)',
            ],
            reflectionPrompts: [
              'Which time blocks were most effective for me?',
              'What distractions interrupted my planned blocks?',
              'How can I better estimate time for different tasks?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'skill-building', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 3,
            title: 'Distraction Audit',
            description:
                'Identify and eliminate the hidden productivity killers in your environment.',
            completionCriteria:
                'Complete a 24-hour distraction log and implement 3 elimination strategies',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/distraction-audit-checklist',
              'Digital Wellness Apps Comparison',
            ],
            reflectionPrompts: [
              'What are my biggest sources of distraction?',
              'Which distractions serve a purpose vs. which are purely wasteful?',
              'What environmental changes can I make to support focus?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'assessment', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 4,
            title: 'Pomodoro Technique Implementation',
            description:
                'Master the Pomodoro Technique for sustained focus and natural break rhythms.',
            completionCriteria:
                'Complete 12 successful Pomodoro sessions over 4 days',
            estimatedDurationMinutes: 40,
            resources: [
              'https://example.com/pomodoro-guide',
              'Recommended Pomodoro Timer Apps',
            ],
            reflectionPrompts: [
              'How did my focus change during 25-minute intervals?',
              'What types of tasks work best with Pomodoro?',
              'How can I optimize my break activities?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'technique', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 5,
            title: 'Deep Work Reflection',
            description:
                'Analyze your progress and identify patterns in your most productive periods.',
            completionCriteria:
                'Write a 500-word reflection on your productivity journey and insights gained',
            estimatedDurationMinutes: 25,
            resources: [
              'https://example.com/reflection-framework',
              'Productivity Journal Template',
            ],
            reflectionPrompts: [
              'What techniques had the biggest impact on my focus?',
              'When am I naturally most productive?',
              'What challenges do I still need to address?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'reflection', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 6,
            title: 'Habit Sustaining System',
            description:
                'Create a sustainable system to maintain your new productivity habits long-term.',
            completionCriteria:
                'Design and implement a weekly review system with accountability measures',
            estimatedDurationMinutes: 25,
            resources: [
              'https://example.com/habit-tracking-systems',
              'Weekly Review Template',
            ],
            reflectionPrompts: [
              'How will I maintain these habits when motivation wanes?',
              'Who can support me in staying accountable?',
              'What early warning signs indicate I\'m slipping back into old patterns?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'sustainability', 'difficulty': 'medium'},
          ),
        ],
      },

      // Mindset & Resilience → "Mind Armor"
      {
        'path': GuidedPath(
          name: 'Mind Armor',
          category: 'Mindset & Resilience',
          description:
              'Build unshakeable mental resilience and develop a growth mindset that thrives under pressure.',
          stepCount: 6,
          targetUserTier: 'paid',
          imageUrl: 'assets/guided-paths/mind-armor.jpg',
          estimatedCompletionTimeMinutes: 210, // 3.5 hours total
          difficultyLevel: 'intermediate',
          prerequisites: [],
          isActive: true,
          createdAt: now,
          metadata: {
            'featured': false,
            'category_order': 2,
            'tags': ['mindset', 'resilience', 'mental-health', 'growth'],
          },
        ),
        'steps': [
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 1,
            title: 'Limiting Beliefs Identification',
            description:
                'Uncover and challenge the hidden beliefs that hold you back from reaching your potential.',
            completionCriteria:
                'Identify 5 limiting beliefs and write evidence-based challenges for each',
            estimatedDurationMinutes: 40,
            resources: [
              'https://example.com/limiting-beliefs-worksheet',
              'Cognitive Distortions Reference Guide',
            ],
            reflectionPrompts: [
              'Where did these beliefs originate?',
              'How have these beliefs served or hindered me?',
              'What evidence contradicts these limiting beliefs?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'discovery', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 2,
            title: 'Cognitive Reframing Mastery',
            description:
                'Learn to reframe negative thoughts into empowering perspectives that fuel growth.',
            completionCriteria:
                'Practice cognitive reframing for 7 days, documenting 3 reframes daily',
            estimatedDurationMinutes: 35,
            resources: [
              'https://example.com/cognitive-reframing-techniques',
              'Thought Record Template',
            ],
            reflectionPrompts: [
              'Which reframing techniques work best for me?',
              'How does my emotional state change after reframing?',
              'What patterns do I notice in my negative thinking?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'skill-building', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 3,
            title: 'Micro-Wins Strategy',
            description:
                'Build momentum and confidence through strategic celebration of small victories.',
            completionCriteria: 'Track and celebrate 21 micro-wins over 7 days',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/micro-wins-tracker',
              'Celebration Rituals Guide',
            ],
            reflectionPrompts: [
              'How do micro-wins affect my motivation and confidence?',
              'What types of achievements do I tend to overlook?',
              'How can I make celebration a consistent habit?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'practice', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 4,
            title: 'Stress Toolkit Development',
            description:
                'Create a personalized toolkit of stress management techniques for challenging moments.',
            completionCriteria:
                'Develop and test 5 stress management techniques, rating their effectiveness',
            estimatedDurationMinutes: 45,
            resources: [
              'https://example.com/stress-management-techniques',
              'Breathing Exercises Audio Guide',
            ],
            reflectionPrompts: [
              'Which techniques help me most in high-stress situations?',
              'How can I remember to use these tools when I need them most?',
              'What early warning signs indicate I need to use my toolkit?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'toolkit', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 5,
            title: 'Affirmations & Self-Talk',
            description:
                'Develop empowering self-talk patterns that reinforce your resilience and capabilities.',
            completionCriteria:
                'Create 10 personal affirmations and practice them daily for 5 days',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/effective-affirmations-guide',
              'Self-Talk Transformation Workbook',
            ],
            reflectionPrompts: [
              'Which affirmations feel most authentic and powerful to me?',
              'How has my internal dialogue changed?',
              'What resistance do I notice to positive self-talk?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'practice', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 6,
            title: 'Growth Mindset Integration',
            description:
                'Integrate growth mindset principles into your daily life and decision-making.',
            completionCriteria:
                'Complete a growth mindset assessment and create an integration plan',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/growth-mindset-assessment',
              'Fixed vs Growth Mindset Examples',
            ],
            reflectionPrompts: [
              'How has my relationship with challenges and failures changed?',
              'Where do I still notice fixed mindset patterns?',
              'How will I continue developing my growth mindset?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'integration', 'difficulty': 'medium'},
          ),
        ],
      },

      // Habit Formation → "Habit Forge"
      {
        'path': GuidedPath(
          name: 'Habit Forge',
          category: 'Habit Formation',
          description:
              'Master the science of habit formation to create lasting positive changes in your life.',
          stepCount: 6,
          targetUserTier: 'paid',
          imageUrl: 'assets/guided-paths/habit-forge.jpg',
          estimatedCompletionTimeMinutes: 195, // 3.25 hours total
          difficultyLevel: 'intermediate',
          prerequisites: [],
          isActive: true,
          createdAt: now,
          metadata: {
            'featured': false,
            'category_order': 3,
            'tags': ['habits', 'behavior-change', 'consistency', 'systems'],
          },
        ),
        'steps': [
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 1,
            title: 'Trigger Selection',
            description:
                'Identify and design powerful environmental triggers that automatically prompt your desired behaviors.',
            completionCriteria:
                'Select and test 3 different triggers for a new habit over 5 days',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/habit-triggers-guide',
              'Environmental Design Checklist',
            ],
            reflectionPrompts: [
              'Which triggers feel most natural and automatic?',
              'How can I make my triggers more obvious and compelling?',
              'What environmental changes support my new habits?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'foundation', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 2,
            title: 'Habit Loop Design',
            description:
                'Design complete habit loops with clear cues, routines, and rewards.',
            completionCriteria:
                'Map out 3 complete habit loops and implement them for 7 days',
            estimatedDurationMinutes: 35,
            resources: [
              'https://example.com/habit-loop-framework',
              'Habit Loop Design Template',
            ],
            reflectionPrompts: [
              'Which rewards are most motivating for me?',
              'How can I make the routine as easy as possible?',
              'What makes some habit loops more successful than others?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'design', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 3,
            title: 'Micro Trials',
            description:
                'Start with tiny versions of your habits to build consistency and confidence.',
            completionCriteria:
                'Complete 21 consecutive days of micro-habit practice',
            estimatedDurationMinutes: 35,
            resources: [
              'https://example.com/micro-habits-guide',
              'Tiny Habits Method Overview',
            ],
            reflectionPrompts: [
              'How does starting small affect my motivation?',
              'Which micro-habits naturally want to grow larger?',
              'What obstacles arise even with tiny habits?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'practice', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 4,
            title: 'Tracking & Measurement',
            description:
                'Implement effective tracking systems to monitor progress and maintain motivation.',
            completionCriteria:
                'Set up and use 3 different tracking methods for 14 days',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/habit-tracking-methods',
              'Digital vs Analog Tracking Comparison',
            ],
            reflectionPrompts: [
              'Which tracking methods keep me most engaged?',
              'How does tracking affect my motivation and consistency?',
              'What patterns do I notice in my habit performance?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'measurement', 'difficulty': 'easy'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 5,
            title: 'Accountability Systems',
            description:
                'Create accountability systems that support your habit formation journey.',
            completionCriteria:
                'Establish 2 accountability systems and use them for 10 days',
            estimatedDurationMinutes: 30,
            resources: [
              'https://example.com/accountability-systems',
              'Habit Buddy Matching Guide',
            ],
            reflectionPrompts: [
              'What type of accountability works best for me?',
              'How do I respond to different forms of accountability?',
              'Who in my life can best support my habit goals?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'support', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 6,
            title: 'Habit Stacking',
            description:
                'Learn to stack new habits onto existing ones for effortless integration.',
            completionCriteria:
                'Create and implement 3 habit stacks, tracking success for 14 days',
            estimatedDurationMinutes: 35,
            resources: [
              'https://example.com/habit-stacking-guide',
              'Existing Habits Inventory Worksheet',
            ],
            reflectionPrompts: [
              'Which existing habits make the best anchors?',
              'How does habit stacking affect my overall routine?',
              'What makes some stacks more successful than others?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'integration', 'difficulty': 'medium'},
          ),
        ],
      },

      // Life Design → "Vision Blueprint"
      {
        'path': GuidedPath(
          name: 'Vision Blueprint',
          category: 'Life Design',
          description:
              'Design a compelling vision for your future and create a strategic roadmap to achieve it.',
          stepCount: 6,
          targetUserTier: 'paid',
          imageUrl: 'assets/guided-paths/vision-blueprint.jpg',
          estimatedCompletionTimeMinutes: 240, // 4 hours total
          difficultyLevel: 'advanced',
          prerequisites: [],
          isActive: true,
          createdAt: now,
          metadata: {
            'featured': false,
            'category_order': 4,
            'tags': ['life-design', 'vision', 'planning', 'goals', 'strategy'],
          },
        ),
        'steps': [
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 1,
            title: 'Values Clarification',
            description:
                'Identify and prioritize your core values to create an authentic foundation for your life design.',
            completionCriteria:
                'Complete values assessment and rank your top 7 core values',
            estimatedDurationMinutes: 45,
            resources: [
              'https://example.com/values-assessment',
              'Core Values Reference List',
            ],
            reflectionPrompts: [
              'Which values feel most essential to who I am?',
              'How well is my current life aligned with these values?',
              'Where do I notice conflicts between my values and actions?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'foundation', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 2,
            title: 'Vision Creation',
            description:
                'Craft a compelling, detailed vision of your ideal future across all life domains.',
            completionCriteria:
                'Write a comprehensive 1000-word vision statement covering 5 life areas',
            estimatedDurationMinutes: 50,
            resources: [
              'https://example.com/vision-creation-guide',
              'Life Domains Framework',
            ],
            reflectionPrompts: [
              'What does success look like in each area of my life?',
              'How do these different areas connect and support each other?',
              'What excites me most about this vision?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'creation', 'difficulty': 'hard'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 3,
            title: 'Backcasting Strategy',
            description:
                'Work backwards from your vision to identify key milestones and decision points.',
            completionCriteria:
                'Create a backcasting timeline with major milestones for the next 5 years',
            estimatedDurationMinutes: 40,
            resources: [
              'https://example.com/backcasting-method',
              'Timeline Creation Template',
            ],
            reflectionPrompts: [
              'What major transitions or changes need to happen?',
              'Which milestones are most critical to achieving my vision?',
              'What potential obstacles or challenges do I foresee?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'strategy', 'difficulty': 'hard'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 4,
            title: 'Milestone Planning',
            description:
                'Break down your major milestones into specific, actionable goals and projects.',
            completionCriteria:
                'Define 3 major milestones with detailed action plans for each',
            estimatedDurationMinutes: 45,
            resources: [
              'https://example.com/milestone-planning-framework',
              'Project Planning Template',
            ],
            reflectionPrompts: [
              'What skills or resources do I need to develop?',
              'Which milestones depend on others or external factors?',
              'How can I make progress on multiple milestones simultaneously?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'planning', 'difficulty': 'hard'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 5,
            title: 'Roadmap Creation',
            description:
                'Create a detailed roadmap with specific actions, timelines, and success metrics.',
            completionCriteria:
                'Develop a 12-month roadmap with quarterly goals and monthly actions',
            estimatedDurationMinutes: 40,
            resources: [
              'https://example.com/roadmap-template',
              'Goal Setting and Tracking Tools',
            ],
            reflectionPrompts: [
              'How will I measure progress toward my vision?',
              'What adjustments might I need to make along the way?',
              'How does this roadmap align with my current commitments?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'implementation', 'difficulty': 'medium'},
          ),
          PathStep(
            pathId: '', // Will be set after path creation
            stepNumber: 6,
            title: 'Review & Iteration System',
            description:
                'Establish a system for regularly reviewing and updating your vision and roadmap.',
            completionCriteria:
                'Create a review schedule and complete your first quarterly review',
            estimatedDurationMinutes: 20,
            resources: [
              'https://example.com/vision-review-process',
              'Quarterly Review Template',
            ],
            reflectionPrompts: [
              'How has my vision evolved since I first created it?',
              'What have I learned about myself through this process?',
              'How will I stay connected to my vision during busy periods?',
            ],
            isActive: true,
            createdAt: now,
            metadata: {'step_type': 'sustainability', 'difficulty': 'medium'},
          ),
        ],
      },
    ];
  }

  /// Seeds the default guided paths and their steps into Firestore
  /// Returns a map with path IDs as keys and lists of step IDs as values
  static Future<Map<String, List<String>>> seedDefaultPathsWithSteps() async {
    final pathsWithSteps = getDefaultPathsWithSteps();
    final result = <String, List<String>>{};

    for (final pathData in pathsWithSteps) {
      final guidedPath = pathData['path'] as GuidedPath;
      final steps = pathData['steps'] as List<PathStep>;

      // Create the guided path first
      final pathId = await FirestoreService.createGuidedPath(guidedPath);

      // Update steps with the correct pathId and create them
      final updatedSteps = steps
          .map((step) => step.copyWith(pathId: pathId))
          .toList();
      final stepIds = await FirestoreService.createPathSteps(updatedSteps);

      result[pathId] = stepIds;
    }

    return result;
  }

  /// Checks if guided paths already exist in Firestore
  /// Returns true if paths exist, false if the collection is empty
  static Future<bool> pathsExist() async {
    final count = await FirestoreService.getGuidedPathCount();
    return count > 0;
  }

  /// Seeds paths only if they don't already exist
  /// Returns the map of created path and step IDs, or empty map if paths already exist
  static Future<Map<String, List<String>>> seedIfEmpty() async {
    if (await pathsExist()) {
      return {};
    }
    return await seedDefaultPathsWithSteps();
  }

  /// Gets the list of default path categories
  static List<String> getDefaultCategories() {
    return [
      'Focus & Productivity',
      'Mindset & Resilience',
      'Habit Formation',
      'Life Design',
    ];
  }

  /// Gets the starter path for free users
  static String getStarterPathName() {
    return 'Zero-to-Flow';
  }
}
