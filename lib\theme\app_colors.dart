import 'package:flutter/material.dart';

/// Comprehensive color palette for the Upshift app
/// Following the "Growth Through Clarity" design philosophy
class AppColors {
  // Primary Colors - Growth & Progress
  static const Color primary = Color(0xFF2E7D32); // Forest Green
  static const Color primaryVariant = Color(0xFF1B5E20); // Deep Forest
  static const Color primaryContainer = Color(0xFFE8F5E8); // Light Green Container
  static const Color primaryContainerDark = Color(0xFF1B4D1F); // Dark Green Container
  static const Color onPrimary = Color(0xFFFFFFFF); // White on primary

  // Secondary Colors - Transformation & Insight
  static const Color secondary = Color(0xFF5E35B1); // Deep Purple
  static const Color secondaryVariant = Color(0xFF4527A0); // Royal Purple
  static const Color secondaryContainer = Color(0xFFF3E5F5); // Light Purple Container
  static const Color secondaryContainerDark = Color(0xFF3A1C71); // Dark Purple Container
  static const Color onSecondary = Color(0xFFFFFFFF); // White on secondary

  // Accent Colors
  static const Color success = Color(0xFF4CAF50); // Success Green
  static const Color warning = Color(0xFFFF9800); // Warm Orange
  static const Color error = Color(0xFFF44336); // Coral Red
  static const Color info = Color(0xFF2196F3); // Sky Blue
  static const Color onError = Color(0xFFFFFFFF); // White on error

  // Surface Colors - Light Theme
  static const Color surfaceLight = Color(0xFFFAFAFA); // Light Surface
  static const Color backgroundLight = Color(0xFFFFFFFF); // White Background
  static const Color onSurfaceLight = Color(0xFF212121); // Dark text on light surface
  static const Color onBackgroundLight = Color(0xFF424242); // Dark text on light background

  // Surface Colors - Dark Theme
  static const Color surfaceDark = Color(0xFF121212); // Dark Surface
  static const Color backgroundDark = Color(0xFF1E1E1E); // Dark Background
  static const Color onSurfaceDark = Color(0xFFE0E0E0); // Light text on dark surface
  static const Color onBackgroundDark = Color(0xFFFFFFFF); // White text on dark background

  // Category-Specific Colors (Enhanced from existing implementation)
  static const Color focusProductivity = Color(0xFF1976D2); // Professional Blue
  static const Color mindsetResilience = Color(0xFF388E3C); // Growth Green
  static const Color habitFormation = Color(0xFFF57C00); // Energy Orange
  static const Color lifeDesign = Color(0xFF7B1FA2); // Vision Purple

  // Neutral Grays
  static const Color gray50 = Color(0xFFFAFAFA);
  static const Color gray100 = Color(0xFFF5F5F5);
  static const Color gray200 = Color(0xFFEEEEEE);
  static const Color gray300 = Color(0xFFE0E0E0);
  static const Color gray400 = Color(0xFFBDBDBD);
  static const Color gray500 = Color(0xFF9E9E9E);
  static const Color gray600 = Color(0xFF757575);
  static const Color gray700 = Color(0xFF616161);
  static const Color gray800 = Color(0xFF424242);
  static const Color gray900 = Color(0xFF212121);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryVariant],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, secondaryVariant],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [success, Color(0xFF2E7D32)],
  );

  // Chat-specific colors
  static const Color userMessageBackground = primary;
  static const Color aiMessageBackground = Color(0xFFF5F5F5);
  static const Color aiMessageBackgroundDark = Color(0xFF2C2C2C);
  static const Color chatInputBackground = Color(0xFFF8F9FA);
  static const Color chatInputBackgroundDark = Color(0xFF1F1F1F);

  // Progress and Achievement Colors
  static const Color progressTrack = Color(0xFFE0E0E0);
  static const Color progressTrackDark = Color(0xFF424242);
  static const Color achievementGold = Color(0xFFFFD700);
  static const Color achievementSilver = Color(0xFFC0C0C0);
  static const Color achievementBronze = Color(0xFFCD7F32);

  /// Get category color by name
  static Color getCategoryColor(String category) {
    switch (category) {
      case 'Focus & Productivity':
        return focusProductivity;
      case 'Mindset & Resilience':
        return mindsetResilience;
      case 'Habit Formation':
        return habitFormation;
      case 'Life Design':
        return lifeDesign;
      default:
        return primary;
    }
  }

  /// Get category color with opacity
  static Color getCategoryColorWithOpacity(String category, double opacity) {
    return getCategoryColor(category).withOpacity(opacity);
  }

  /// Get difficulty color
  static Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return success;
      case 'intermediate':
        return warning;
      case 'advanced':
        return error;
      default:
        return gray500;
    }
  }

  /// Get tier-based colors
  static Color getTierColor(String tier) {
    switch (tier.toLowerCase()) {
      case 'free':
        return success;
      case 'paid':
      case 'premium':
        return achievementGold;
      default:
        return gray500;
    }
  }

  /// Get status colors
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return success;
      case 'in_progress':
      case 'current':
        return primary;
      case 'locked':
      case 'disabled':
        return gray400;
      case 'error':
      case 'failed':
        return error;
      case 'warning':
        return warning;
      default:
        return gray500;
    }
  }

  /// Create a color with adaptive opacity based on theme brightness
  static Color adaptiveColor(Color color, Brightness brightness, {double lightOpacity = 1.0, double darkOpacity = 0.8}) {
    return brightness == Brightness.light 
        ? color.withOpacity(lightOpacity)
        : color.withOpacity(darkOpacity);
  }

  /// Get surface color with elevation tint
  static Color getSurfaceWithElevation(ColorScheme colorScheme, double elevation) {
    if (colorScheme.brightness == Brightness.dark) {
      // Apply elevation tint for dark theme
      final tintOpacity = (elevation / 24).clamp(0.0, 0.15);
      return Color.alphaBlend(
        colorScheme.primary.withOpacity(tintOpacity),
        colorScheme.surface,
      );
    }
    return colorScheme.surface;
  }
}
