import 'package:firebase_ai/firebase_ai.dart';
import '../models/models.dart';

class ChatTitleGenerator {
  final GenerativeModel _model;

  ChatTitleGenerator()
    : _model = FirebaseAI.googleAI().generativeModel(
        model: 'gemini-2.5-flash',
        safetySettings: [
          SafetySetting(
            HarmCategory.dangerousContent,
            HarmBlockThreshold.none,
            null,
          ),
        ],
      );

  /// Generates a short title (3-10 words) for a chat based on its messages
  Future<String> generateTitle(List<Message> messages) async {
    try {
      // Filter out image messages and get only text content
      final textMessages = messages
          .where((msg) => msg.type == 'text' && msg.textContent != null)
          .map((msg) => msg.textContent!)
          .toList();

      if (textMessages.isEmpty) {
        return 'New Chat';
      }

      // Concatenate all text messages with some context
      final conversationText = textMessages
          .take(10)
          .join('\n'); // Limit to first 10 messages

      // Create a prompt for title generation
      final prompt =
          '''
Based on the following conversation, generate a short, descriptive title (3-10 words) that captures the main topic or theme. The title should be concise and informative.

Conversation:
$conversationText

Generate only the title, nothing else. Do not use quotes or special formatting.
''';

      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);

      final generatedTitle = response.text?.trim();

      if (generatedTitle != null && generatedTitle.isNotEmpty) {
        // Ensure the title is within reasonable length (max 100 characters)
        return generatedTitle.length > 100
            ? generatedTitle.substring(0, 100).trim()
            : generatedTitle;
      }

      return 'New Chat';
    } catch (e) {
      // If title generation fails, return a default title
      return 'New Chat';
    }
  }
}
