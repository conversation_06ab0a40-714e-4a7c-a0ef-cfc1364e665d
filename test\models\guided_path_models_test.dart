import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('GuidedPath Model Tests', () {
    test('should create GuidedPath with required fields', () {
      final now = DateTime.now();
      final guidedPath = GuidedPath(
        name: 'Test Path',
        category: 'Focus & Productivity',
        description: 'A test guided path',
        stepCount: 5,
        targetUserTier: 'free',
        createdAt: now,
      );

      expect(guidedPath.name, 'Test Path');
      expect(guidedPath.category, 'Focus & Productivity');
      expect(guidedPath.description, 'A test guided path');
      expect(guidedPath.stepCount, 5);
      expect(guidedPath.targetUserTier, 'free');
      expect(guidedPath.isActive, true); // Default value
      expect(guidedPath.createdAt, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final guidedPath = GuidedPath(
        id: 'test-id',
        name: 'Test Path',
        category: 'Focus & Productivity',
        description: 'A test guided path',
        stepCount: 5,
        targetUserTier: 'free',
        imageUrl: 'https://example.com/image.jpg',
        estimatedCompletionTimeMinutes: 120,
        difficultyLevel: 'beginner',
        prerequisites: ['Basic knowledge'],
        isActive: true,
        createdAt: now,
        updatedAt: now,
        metadata: {'test': 'value'},
      );

      // Serialize to JSON
      final json = guidedPath.toJson();
      
      // Verify JSON structure
      expect(json['id'], 'test-id');
      expect(json['name'], 'Test Path');
      expect(json['category'], 'Focus & Productivity');
      expect(json['stepCount'], 5);
      expect(json['targetUserTier'], 'free');
      expect(json['isActive'], true);
      expect(json['prerequisites'], ['Basic knowledge']);
      expect(json['metadata'], {'test': 'value'});

      // Deserialize from JSON
      final deserializedPath = GuidedPath.fromJson(json);
      
      // Verify deserialized object
      expect(deserializedPath.id, guidedPath.id);
      expect(deserializedPath.name, guidedPath.name);
      expect(deserializedPath.category, guidedPath.category);
      expect(deserializedPath.stepCount, guidedPath.stepCount);
      expect(deserializedPath.targetUserTier, guidedPath.targetUserTier);
      expect(deserializedPath.isActive, guidedPath.isActive);
      expect(deserializedPath.prerequisites, guidedPath.prerequisites);
      expect(deserializedPath.metadata, guidedPath.metadata);
    });

    test('should create copy with updated fields', () {
      final now = DateTime.now();
      final original = GuidedPath(
        name: 'Original Path',
        category: 'Focus & Productivity',
        description: 'Original description',
        stepCount: 5,
        targetUserTier: 'free',
        createdAt: now,
      );

      final updated = original.copyWith(
        name: 'Updated Path',
        stepCount: 7,
        targetUserTier: 'paid',
      );

      expect(updated.name, 'Updated Path');
      expect(updated.stepCount, 7);
      expect(updated.targetUserTier, 'paid');
      // Unchanged fields should remain the same
      expect(updated.category, original.category);
      expect(updated.description, original.description);
      expect(updated.createdAt, original.createdAt);
    });
  });

  group('PathStep Model Tests', () {
    test('should create PathStep with required fields', () {
      final now = DateTime.now();
      final pathStep = PathStep(
        pathId: 'test-path-id',
        stepNumber: 1,
        title: 'Test Step',
        description: 'A test step description',
        completionCriteria: 'Complete the test',
        createdAt: now,
      );

      expect(pathStep.pathId, 'test-path-id');
      expect(pathStep.stepNumber, 1);
      expect(pathStep.title, 'Test Step');
      expect(pathStep.description, 'A test step description');
      expect(pathStep.completionCriteria, 'Complete the test');
      expect(pathStep.isActive, true); // Default value
      expect(pathStep.createdAt, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final pathStep = PathStep(
        id: 'step-id',
        pathId: 'test-path-id',
        stepNumber: 2,
        title: 'Test Step',
        description: 'A test step description',
        completionCriteria: 'Complete the test',
        estimatedDurationMinutes: 30,
        resources: ['Resource 1', 'Resource 2'],
        reflectionPrompts: ['Prompt 1', 'Prompt 2'],
        isActive: true,
        createdAt: now,
        metadata: {'step_type': 'practice'},
      );

      // Serialize to JSON
      final json = pathStep.toJson();
      
      // Verify JSON structure
      expect(json['id'], 'step-id');
      expect(json['pathId'], 'test-path-id');
      expect(json['stepNumber'], 2);
      expect(json['title'], 'Test Step');
      expect(json['resources'], ['Resource 1', 'Resource 2']);
      expect(json['reflectionPrompts'], ['Prompt 1', 'Prompt 2']);
      expect(json['metadata'], {'step_type': 'practice'});

      // Deserialize from JSON
      final deserializedStep = PathStep.fromJson(json);
      
      // Verify deserialized object
      expect(deserializedStep.id, pathStep.id);
      expect(deserializedStep.pathId, pathStep.pathId);
      expect(deserializedStep.stepNumber, pathStep.stepNumber);
      expect(deserializedStep.title, pathStep.title);
      expect(deserializedStep.resources, pathStep.resources);
      expect(deserializedStep.reflectionPrompts, pathStep.reflectionPrompts);
      expect(deserializedStep.metadata, pathStep.metadata);
    });
  });

  group('UserPathProgress Model Tests', () {
    test('should create UserPathProgress with required fields', () {
      final now = DateTime.now();
      final progress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        startedDate: now,
        lastAccessedDate: now,
      );

      expect(progress.userId, 'user-123');
      expect(progress.pathId, 'path-456');
      expect(progress.currentStepNumber, 1); // Default value
      expect(progress.completedSteps, []); // Default value
      expect(progress.status, 'not_started'); // Default value
      expect(progress.startedDate, now);
      expect(progress.lastAccessedDate, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final progress = UserPathProgress(
        id: 'progress-id',
        userId: 'user-123',
        pathId: 'path-456',
        currentStepNumber: 3,
        completedSteps: [1, 2],
        status: 'in_progress',
        startedDate: now,
        lastAccessedDate: now,
        completionDate: now,
        progressMetadata: {'notes': 'Making good progress'},
      );

      // Serialize to JSON
      final json = progress.toJson();
      
      // Verify JSON structure
      expect(json['id'], 'progress-id');
      expect(json['userId'], 'user-123');
      expect(json['pathId'], 'path-456');
      expect(json['currentStepNumber'], 3);
      expect(json['completedSteps'], [1, 2]);
      expect(json['status'], 'in_progress');
      expect(json['progressMetadata'], {'notes': 'Making good progress'});

      // Deserialize from JSON
      final deserializedProgress = UserPathProgress.fromJson(json);
      
      // Verify deserialized object
      expect(deserializedProgress.id, progress.id);
      expect(deserializedProgress.userId, progress.userId);
      expect(deserializedProgress.pathId, progress.pathId);
      expect(deserializedProgress.currentStepNumber, progress.currentStepNumber);
      expect(deserializedProgress.completedSteps, progress.completedSteps);
      expect(deserializedProgress.status, progress.status);
      expect(deserializedProgress.progressMetadata, progress.progressMetadata);
    });

    test('should provide helper methods for progress tracking', () {
      final now = DateTime.now();
      final progress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        currentStepNumber: 3,
        completedSteps: [1, 2, 4],
        status: 'in_progress',
        startedDate: now,
        lastAccessedDate: now,
      );

      // Test status helpers
      expect(progress.isInProgress, true);
      expect(progress.isCompleted, false);
      expect(progress.isNotStarted, false);
      expect(progress.isPaused, false);

      // Test progress percentage
      expect(progress.getProgressPercentage(6), 0.5); // 3 completed out of 6 total
      expect(progress.getProgressPercentage(0), 0.0); // Edge case

      // Test step completion check
      expect(progress.isStepCompleted(1), true);
      expect(progress.isStepCompleted(2), true);
      expect(progress.isStepCompleted(3), false);
      expect(progress.isStepCompleted(4), true);
    });

    test('should handle completed status correctly', () {
      final now = DateTime.now();
      final completedProgress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        currentStepNumber: 6,
        completedSteps: [1, 2, 3, 4, 5, 6],
        status: 'completed',
        startedDate: now,
        lastAccessedDate: now,
        completionDate: now,
      );

      expect(completedProgress.isCompleted, true);
      expect(completedProgress.isInProgress, false);
      expect(completedProgress.getProgressPercentage(6), 1.0);
    });
  });
}
