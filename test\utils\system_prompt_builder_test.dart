import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/utils/system_prompt_builder.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('SystemPromptBuilder', () {
    test('builds basic system prompt without user profile', () {
      final prompt = SystemPromptBuilder.buildSystemPrompt(
        currentTime: DateTime(2024, 1, 15, 10, 30),
        chatObjective: 'Test coaching session',
      );

      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('Current Context'));
      expect(prompt, contains('Test coaching session'));
      expect(prompt, contains('Response Guidelines'));
      expect(prompt, contains('2024-01-15T10:30:00.000'));
    });

    test('builds comprehensive system prompt with user profile', () {
      final userProfile = UserProfile(
        userId: 'test-user-123',
        name: '<PERSON>',
        age: 30,
        gender: 'male',
        familyStatus: 'married',
        location: Location(town: 'San Francisco', country: 'USA'),
        likes: ['reading', 'hiking', 'technology'],
        dislikes: ['loud noises', 'crowded places'],
        personalityTraits: ['analytical', 'introverted'],
        goals: [
          Goal(
            id: 'goal-1',
            description: 'Learn a new programming language',
            status: 'in_progress',
            createdAt: DateTime(2024, 1, 1),
          ),
        ],
        facts: [
          Fact(key: 'occupation', value: 'Software Engineer'),
          Fact(key: 'experience', value: '5 years'),
        ],
        family: [
          RelationInfo(
            name: 'Jane Doe',
            age: 28,
            relation: 'spouse',
            otherInfo: ['works in marketing'],
          ),
        ],
        preferences: {
          'communication_style': 'direct',
          'session_length': 'medium',
        },
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime(2024, 1, 10),
          sources: [],
        ),
      );

      final prompt = SystemPromptBuilder.buildSystemPrompt(
        userProfile: userProfile,
        currentTime: DateTime(2024, 1, 15, 14, 45),
        chatObjective: 'Career development coaching',
        additionalInstructions: [
          'Focus on technical skills',
          'Provide actionable steps',
        ],
      );

      // Check that all major sections are included
      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('Current Context'));
      expect(prompt, contains('User Profile'));
      expect(prompt, contains('Session Objective'));
      expect(prompt, contains('Additional Instructions'));
      expect(prompt, contains('Response Guidelines'));

      // Check user profile information is included
      expect(prompt, contains('John Doe'));
      expect(prompt, contains('30'));
      expect(prompt, contains('male'));
      expect(prompt, contains('married'));
      expect(prompt, contains('San Francisco, USA'));
      expect(prompt, contains('reading, hiking, technology'));
      expect(prompt, contains('loud noises, crowded places'));
      expect(prompt, contains('analytical, introverted'));
      expect(prompt, contains('Learn a new programming language'));
      expect(prompt, contains('Software Engineer'));
      expect(prompt, contains('Jane Doe'));
      expect(prompt, contains('spouse'));
      expect(prompt, contains('direct'));

      // Check objective and instructions
      expect(prompt, contains('Career development coaching'));
      expect(prompt, contains('Focus on technical skills'));
      expect(prompt, contains('Provide actionable steps'));

      // Check timestamp
      expect(prompt, contains('2024-01-15T14:45:00.000'));
    });

    test('handles empty or null user profile gracefully', () {
      final prompt = SystemPromptBuilder.buildSystemPrompt(
        userProfile: null,
        currentTime: DateTime(2024, 1, 15),
      );

      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('Current Context'));
      expect(prompt, contains('Response Guidelines'));
      expect(prompt, isNot(contains('User Profile')));
    });

    test('handles user profile with minimal information', () {
      final userProfile = UserProfile(
        userId: 'minimal-user',
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime(2024, 1, 10),
          sources: [],
        ),
      );

      final prompt = SystemPromptBuilder.buildSystemPrompt(
        userProfile: userProfile,
        currentTime: DateTime(2024, 1, 15),
      );

      expect(prompt, contains('User Profile'));
      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('Response Guidelines'));
    });
  });
}
