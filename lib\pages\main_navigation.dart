import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:upshift/pages/home.dart';
import 'package:upshift/pages/chat.dart';
import 'package:upshift/pages/account.dart';
import 'package:upshift/pages/admin/persona_seeder_page.dart';
import 'package:upshift/services/firestore.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _selectedIndex = 0;
  models.User? _currentUser;
  bool _isLoading = true;

  // Base pages that are always available
  static const List<Widget> _basePages = <Widget>[
    HomePage(),
    ChatPage(),
    AccountPage(),
  ];

  // Admin page
  static const Widget _adminPage = PersonaSeederPage();

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        final user = await FirestoreService.getUser(firebaseUser.uid);
        setState(() {
          _currentUser = user;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Get dynamic list of pages based on user permissions
  List<Widget> get _pages {
    final pages = List<Widget>.from(_basePages);
    if (_currentUser?.isAdmin == true) {
      pages.add(_adminPage);
    }
    return pages;
  }

  // Get dynamic list of navigation items based on user permissions
  List<BottomNavigationBarItem> get _navigationItems {
    final items = <BottomNavigationBarItem>[
      BottomNavigationBarItem(
        icon: Icon(AppIcons.getNavigationIcon('home', selected: false)),
        activeIcon: Icon(AppIcons.getNavigationIcon('home', selected: true)),
        label: 'Home',
      ),
      BottomNavigationBarItem(
        icon: Icon(AppIcons.getNavigationIcon('chat', selected: false)),
        activeIcon: Icon(AppIcons.getNavigationIcon('chat', selected: true)),
        label: 'Chats',
      ),
      BottomNavigationBarItem(
        icon: Icon(AppIcons.getNavigationIcon('profile', selected: false)),
        activeIcon: Icon(AppIcons.getNavigationIcon('profile', selected: true)),
        label: 'Account',
      ),
    ];

    if (_currentUser?.isAdmin == true) {
      items.add(
        BottomNavigationBarItem(
          icon: Icon(AppIcons.getNavigationIcon('admin', selected: false)),
          activeIcon: Icon(AppIcons.getNavigationIcon('admin', selected: true)),
          label: 'Admin',
        ),
      );
    }

    return items;
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  // Method to refresh user data (can be called when user permissions change)
  Future<void> refreshUserData() async {
    await _loadCurrentUser();
    // If current selected index is out of bounds after refresh, reset to home
    if (_selectedIndex >= _pages.length) {
      setState(() {
        _selectedIndex = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while user data is being fetched
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final pages = _pages;
    final navigationItems = _navigationItems;

    // Ensure selected index is within bounds
    final safeSelectedIndex = _selectedIndex >= pages.length
        ? 0
        : _selectedIndex;

    return Scaffold(
      body: IndexedStack(index: safeSelectedIndex, children: pages),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType
            .fixed, // Ensures all items are visible and properly spaced for 3-4 items
        items: navigationItems,
        currentIndex: safeSelectedIndex,
        selectedItemColor: context.colorScheme.primary,
        unselectedItemColor: context.colorScheme.onSurface.withValues(
          alpha: 0.6,
        ),
        selectedLabelStyle: AppTypography.bottomNavSelected,
        unselectedLabelStyle: AppTypography.bottomNavUnselected,
        backgroundColor: context.colorScheme.surface,
        elevation: AppDimensions.elevationL,
        onTap: (index) {
          // Ensure the tapped index is within bounds
          if (index < pages.length) {
            _onItemTapped(index);
          }
        },
      ),
    );
  }
}
