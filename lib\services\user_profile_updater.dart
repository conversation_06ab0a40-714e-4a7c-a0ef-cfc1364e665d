import 'dart:convert';
import 'package:firebase_ai/firebase_ai.dart';
import 'package:flutter/foundation.dart';
import '../models/models.dart';

class UserProfileUpdater {
  GenerativeModel? _model;

  UserProfileUpdater();

  /// Gets or creates the Gemini model instance
  Future<GenerativeModel> _getModel() async {
    if (_model != null) return _model!;

    _model = FirebaseAI.googleAI().generativeModel(
      model: 'gemini-2.5-flash',
      safetySettings: [
        SafetySetting(
          HarmCategory.dangerousContent,
          HarmBlockThreshold.none,
          null,
        ),
      ],
      generationConfig: GenerationConfig(temperature: 0.3),
    );
    return _model!;
  }

  /// Updates a UserProfile based on chat conversation analysis
  /// Returns the updated UserProfile or null if update fails
  Future<UserProfile?> updateProfileFromConversation({
    required UserProfile currentProfile,
    required List<Message> chatMessages,
    required String chatId,
  }) async {
    try {
      // Filter out image messages and get only text content
      final textMessages = chatMessages
          .where((msg) => msg.type == 'text' && msg.textContent != null)
          .map((msg) => msg.textContent!)
          .toList();

      if (textMessages.isEmpty) {
        return currentProfile; // No text to analyze
      }

      // Create conversation context
      final conversationText = textMessages.join('\n');

      // Load the UserProfile schema
      final schemaJson = await _loadUserProfileSchema();

      // Create the prompt for profile analysis and update
      final prompt = _buildUpdatePrompt(
        currentProfile: currentProfile,
        conversationText: conversationText,
        schemaJson: schemaJson,
        chatId: chatId,
      );

      final content = [Content.text(prompt)];
      final model = await _getModel();
      final response = await model.generateContent(content);

      final responseText = response.text?.trim();
      if (responseText == null || responseText.isEmpty) {
        return null;
      }
      debugPrint('Raw response: $responseText');

      // Parse the JSON response (handle both markdown-wrapped and plain JSON)
      final Map<String, dynamic> updatedProfileJson;
      try {
        final cleanedJson = _extractJsonFromResponse(responseText);
        updatedProfileJson = jsonDecode(cleanedJson);
      } catch (e) {
        debugPrint('Failed to parse JSON response: $e');
        return null;
      }

      // Validate and create updated UserProfile (TimestampDateTimeConverter handles ISO 8601 strings)
      final updatedProfile = UserProfile.fromJson(updatedProfileJson);

      // Ensure userId remains the same
      if (updatedProfile.userId != currentProfile.userId) {
        return null;
      }

      return updatedProfile;
    } catch (e) {
      debugPrint('Error updating user profile from conversation: $e');
      return null;
    }
  }

  /// Converts UserProfile to JSON with proper DateTime serialization for AI analysis
  Map<String, dynamic> _convertUserProfileToJsonForAI(UserProfile profile) {
    final json = profile.toJson();

    // Convert any Firestore Timestamps to ISO 8601 strings recursively
    return convertTimestampsToStrings(json);
  }

  /// Recursively converts Firestore Timestamps to ISO 8601 strings in a JSON object
  dynamic convertTimestampsToStrings(dynamic value) {
    if (value == null) return null;

    if (value is Map<String, dynamic>) {
      final result = <String, dynamic>{};
      for (final entry in value.entries) {
        result[entry.key] = convertTimestampsToStrings(entry.value);
      }
      return result;
    }

    if (value is List) {
      return value.map(convertTimestampsToStrings).toList();
    }

    // Convert Firestore Timestamp to ISO 8601 string
    if (value.runtimeType.toString() == 'Timestamp') {
      // Use dynamic access since we can't import cloud_firestore here
      final timestamp = value as dynamic;
      final dateTime = timestamp.toDate() as DateTime;
      return dateTime.toUtc().toIso8601String();
    }

    // Convert DateTime to ISO 8601 string
    if (value is DateTime) {
      return value.toUtc().toIso8601String();
    }

    return value;
  }

  /// Extracts JSON content from Gemini AI response, handling both markdown-wrapped and plain JSON
  String _extractJsonFromResponse(String responseText) {
    final trimmedResponse = responseText.trim();

    // Check if response is wrapped in markdown code blocks
    if (trimmedResponse.startsWith('```json') &&
        trimmedResponse.endsWith('```')) {
      // Extract content between ```json and ```
      final startIndex =
          trimmedResponse.indexOf('```json') + 7; // Length of '```json'
      final endIndex = trimmedResponse.lastIndexOf('```');

      if (startIndex < endIndex) {
        final extractedJson = trimmedResponse
            .substring(startIndex, endIndex)
            .trim();
        debugPrint('Extracted JSON from markdown code block');
        return extractedJson;
      }
    }

    // Check for alternative markdown patterns like ```json\n{...}\n```
    final codeBlockPattern = RegExp(
      r'```(?:json)?\s*\n?(.*?)\n?```',
      dotAll: true,
    );
    final match = codeBlockPattern.firstMatch(trimmedResponse);
    if (match != null) {
      final extractedJson = match.group(1)?.trim() ?? '';
      if (extractedJson.isNotEmpty) {
        debugPrint('Extracted JSON using regex pattern');
        return extractedJson;
      }
    }

    // If no markdown formatting detected, return the original response
    debugPrint('No markdown formatting detected, using raw response');
    return trimmedResponse;
  }

  /// Loads the UserProfile schema from assets
  Future<String> _loadUserProfileSchema() async {
    // For now, return the schema as a string
    // In a real implementation, you might load this from assets
    return '''
{
  "\$id": "https://example.com/schemas/user_profile.json",
  "\$schema": "http://json-schema.org/draft-07/schema#",
  "title": "UserProfile",
  "description": "Structured long‑term memory record for an AI coach user.",
  "type": "object",
  "properties": {
    "userId": {"type": "string", "description": "Unique identifier for the user"},
    "name": {"type": "string", "description": "User's full name"},
    "age": {"type": "integer", "minimum": 0, "description": "User's age in years"},
    "gender": {"type": "string", "enum": ["male", "female", "non-binary", "other", "unspecified"], "description": "User's self‑identified gender"},
    "familyStatus": {"type": "string", "enum": ["single", "married", "partnered", "divorced", "widowed", "unspecified"], "description": "User's marital or family status"},
    "family": {"type": "array", "description": "Information about user's household members and close relations", "items": {"type": "object", "properties": {"name": {"type": "string"}, "age": {"type": "integer", "minimum": 0}, "relation": {"type": "string"}, "otherInfo": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "age", "relation"]}},
    "location": {"type": "object", "description": "Where the user lives", "properties": {"town": {"type": "string"}, "country": {"type": "string"}}, "required": ["country"]},
    "facts": {"type": "array", "description": "Immutable or semi‑immutable known facts about the user", "items": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": ["string", "number", "boolean"]}}, "required": ["key", "value"]}},
    "likes": {"type": "array", "description": "Things the user enjoys", "items": {"type": "string"}},
    "dislikes": {"type": "array", "description": "Things the user dislikes", "items": {"type": "string"}},
    "preferences": {"type": "object", "description": "User's ongoing interaction preferences", "additionalProperties": {"type": ["string", "number", "boolean", "object", "array"]}},
    "goals": {"type": "array", "description": "Short‑ and long‑term goals the user is working toward", "items": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["not_started", "in_progress", "achieved", "abandoned"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "description", "status", "createdAt"]}},
    "personalityTraits": {"type": "array", "description": "Traits inferred from conversation", "items": {"type": "string"}},
    "interactionHistory": {"type": "object", "description": "Metadata about memory updates", "properties": {"lastUpdated": {"type": "string", "format": "date-time"}, "sources": {"type": "array", "items": {"type": "object", "properties": {"sessionId": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["sessionId", "timestamp"]}}}, "required": ["lastUpdated"]}
  },
  "required": ["userId", "interactionHistory"],
  "additionalProperties": false
}
''';
  }

  /// Builds the prompt for updating the user profile
  String _buildUpdatePrompt({
    required UserProfile currentProfile,
    required String conversationText,
    required String schemaJson,
    required String chatId,
  }) {
    // Convert UserProfile to JSON with proper DateTime serialization for AI
    final currentProfileJson = jsonEncode(
      _convertUserProfileToJsonForAI(currentProfile),
    );
    final now = DateTime.now().toUtc().toIso8601String();

    return '''
You are an AI assistant that analyzes conversations to update user profiles. Your task is to analyze the provided conversation and update the user's profile with any new insights, preferences, goals, or behavioral patterns that can be inferred.

CURRENT USER PROFILE:
$currentProfileJson

CONVERSATION TO ANALYZE:
$conversationText

INSTRUCTIONS:
1. Analyze the conversation for new information about the user
2. Update the user profile by:
   - Adding new insights to existing fields (likes, dislikes, facts, goals, personality traits)
   - Updating existing information if contradicted by new evidence
   - Preserving all existing data unless explicitly contradicted
   - Adding new goals if mentioned in the conversation
   - Updating goal statuses if progress is discussed
   - Inferring personality traits from communication style and content

3. CRITICAL: The output must strictly conform to this JSON schema:
$schemaJson

4. Update the interactionHistory:
   - Set lastUpdated to: $now
   - Add a new source entry with sessionId: "$chatId" and timestamp: "$now"

5. Ensure the userId remains unchanged: "${currentProfile.userId}"

6. Return ONLY the updated JSON object, no additional text or formatting.

UPDATED USER PROFILE:
''';
  }
}
